<?php
// hanapp_backend/api/verification/badge_payment_webhook.php
// Handle Xendit webhook notifications for badge subscription payments

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Start output buffering to prevent any unexpected output
ob_start();

require_once '../../config/db_connect.php';

header('Content-Type: application/json');

// Handle GET requests for testing webhook accessibility
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    ob_clean();
    echo json_encode([
        "status" => "ok",
        "message" => "Badge payment webhook is accessible",
        "timestamp" => date('Y-m-d H:i:s'),
        "endpoint" => "badge_payment_webhook.php"
    ]);
    exit();
}

// Only allow POST requests for actual webhook processing
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_clean();
    http_response_code(405);
    echo json_encode(["error" => "Method not allowed"]);
    exit();
}

try {
    // Get webhook payload
    $payload = file_get_contents('php://input');
    $webhookData = json_decode($payload, true);

    if (!$webhookData) {
        throw new Exception("Invalid webhook payload");
    }

    // Log webhook for debugging
    error_log("=== BADGE PAYMENT WEBHOOK START ===");
    error_log("Badge Payment Webhook Received: " . $payload);
    error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
    error_log("Request URI: " . $_SERVER['REQUEST_URI']);
    error_log("User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'N/A'));
    error_log("Timestamp: " . date('Y-m-d H:i:s'));

    // Also log to a specific file for easier tracking
    file_put_contents(
        __DIR__ . '/../../webhook_logs.txt',
        "[" . date('Y-m-d H:i:s') . "] BADGE WEBHOOK CALLED: " . $payload . "\n",
        FILE_APPEND | LOCK_EX
    );

    // Verify webhook signature (optional but recommended for production)
    $xenditWebhookToken = 'ibwb3mKaoLK41jzM8145jUOQSAy9714RFRATVW270sAr'; // From Xendit dashboard
    $receivedSignature = $_SERVER['HTTP_X_CALLBACK_TOKEN'] ?? '';

    error_log("Badge webhook signature received: " . $receivedSignature);

    // Extract invoice data
    $invoiceId = $webhookData['id'] ?? null;
    $externalId = $webhookData['external_id'] ?? null;
    $status = $webhookData['status'] ?? null;
    $amount = $webhookData['amount'] ?? null;
    $paidAt = $webhookData['paid_at'] ?? null;

    if (!$invoiceId || !$externalId || !$status) {
        throw new Exception("Missing required webhook data");
    }

    // Only process badge payments (check external_id format)
    if (!preg_match('/^hanapp_badge_(\d+)_\d+$/', $externalId, $matches)) {
        error_log("Badge webhook: Not a badge payment, ignoring. External ID: " . $externalId);
        ob_clean();
        echo json_encode(["status" => "ignored", "message" => "Not a badge payment"]);
        exit();
    }

    $userId = intval($matches[1]);

    error_log("Badge webhook processing: Invoice ID: $invoiceId, External ID: $externalId, Status: $status, User ID: $userId");

    // Start database transaction
    $conn->begin_transaction();

    try {
        // Update payment record
        $stmt = $conn->prepare("
            UPDATE badge_payments
            SET status = ?, paid_at = ?, updated_at = NOW()
            WHERE xendit_invoice_id = ? AND external_id = ?
        ");

        if ($stmt === false) {
            throw new Exception("Failed to prepare payment update statement");
        }

        $paidAtTimestamp = $paidAt ? date('Y-m-d H:i:s', strtotime($paidAt)) : null;
        $stmt->bind_param("ssss", $status, $paidAtTimestamp, $invoiceId, $externalId);

        if (!$stmt->execute()) {
            throw new Exception("Failed to update payment record: " . $stmt->error);
        }

        $stmt->close();

        // If payment is successful, update user's badge subscription
        if ($status === 'PAID') {
            error_log("Badge webhook: Payment successful, updating user subscription for user ID: $userId");

            // Calculate subscription dates
            $subscriptionStart = date('Y-m-d');
            $subscriptionEnd = date('Y-m-d', strtotime('+1 month'));
            $nextPaymentDue = date('Y-m-d', strtotime('+1 month'));

            // Update user's badge subscription
            $stmt = $conn->prepare("
                UPDATE users
                SET
                    badge_status = 'verified',
                    badge_acquired = 1,
                    badge_subscription_status = 'active',
                    badge_subscription_start = ?,
                    badge_subscription_end = ?,
                    badge_next_payment_due = ?,
                    badge_last_payment_date = NOW(),
                    updated_at = NOW()
                WHERE id = ?
            ");

            if ($stmt === false) {
                throw new Exception("Failed to prepare user update statement");
            }

            $stmt->bind_param("sssi", $subscriptionStart, $subscriptionEnd, $nextPaymentDue, $userId);

            error_log("Badge webhook: About to execute user update for user ID: $userId");
            error_log("Badge webhook: Update values - Start: $subscriptionStart, End: $subscriptionEnd, Next Due: $nextPaymentDue");

            if (!$stmt->execute()) {
                error_log("Badge webhook: Failed to execute user update: " . $stmt->error);
                throw new Exception("Failed to update user badge subscription: " . $stmt->error);
            }

            $affectedRows = $stmt->affected_rows;
            $stmt->close();

            error_log("Badge webhook: User update executed successfully. Affected rows: $affectedRows");

            if ($affectedRows === 0) {
                error_log("Badge webhook: WARNING - No rows were updated for user ID: $userId. User may not exist.");
            } else {
                error_log("Badge webhook: Successfully updated user $userId badge subscription until $subscriptionEnd");
            }
        } else if ($status === 'EXPIRED' || $status === 'FAILED') {
            error_log("Badge webhook: Payment failed/expired for user ID: $userId, status: $status");
        }

        // Commit transaction
        $conn->commit();

        ob_clean();
        echo json_encode([
            "status" => "success",
            "message" => "Badge payment webhook processed successfully",
            "invoice_id" => $invoiceId,
            "external_id" => $externalId,
            "payment_status" => $status,
            "user_id" => $userId
        ]);

    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        throw $e;
    }

} catch (Exception $e) {
    error_log("Badge webhook error: " . $e->getMessage());
    ob_clean();
    http_response_code(400);
    echo json_encode([
        "status" => "error",
        "message" => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
