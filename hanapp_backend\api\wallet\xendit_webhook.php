<?php
// hanapp_backend/api/wallet/xendit_webhook.php
// Handle Xendit webhook notifications for cash-in transactions, badge payments, and job payment invoices

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Start output buffering to prevent any unexpected output
ob_start();

require_once '../../config/db_connect.php';

header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
ob_clean();
http_response_code(405);
echo json_encode(["error" => "Method not allowed"]);
exit();
}

try {
// Get webhook payload
$payload = file_get_contents('php://input');
$webhookData = json_decode($payload, true);

if (!$webhookData) {
throw new Exception("Invalid webhook payload");
}

// Log webhook for debugging
error_log("Xendit Webhook Received: " . $payload);
error_log("Webhook External ID: " . ($webhookData['external_id'] ?? 'NULL'));
error_log("Webhook Status: " . ($webhookData['status'] ?? 'NULL'));

// Also log to a file for easier debugging
file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - Webhook received: " . $payload . "\n",
FILE_APPEND | LOCK_EX
);

// Verify webhook signature (optional but recommended for production)
$xenditWebhookToken = 'ibwb3mKaoLK41jzM8145jUOQSAy9714RFRATVW270sAr'; // From Xendit dashboard
$receivedSignature = $_SERVER['HTTP_X_CALLBACK_TOKEN'] ?? '';

// Optional: Enable signature verification for production
// if ($receivedSignature !== $xenditWebhookToken) {
// throw new Exception("Invalid webhook signature");
// }

error_log("Webhook signature received: " . $receivedSignature);

// Extract invoice data
$invoiceId = $webhookData['id'] ?? null;
$externalId = $webhookData['external_id'] ?? null;
$status = $webhookData['status'] ?? null;
$paidAmount = $webhookData['paid_amount'] ?? null;
$paidAt = $webhookData['paid_at'] ?? null;
$paymentMethod = $webhookData['payment_method'] ?? null;
$paymentChannel = $webhookData['payment_channel'] ?? null;
$fees = $webhookData['fees'] ?? null;

if (!$invoiceId || !$externalId) {
throw new Exception("Missing required webhook data");
}

// Debug: Check external ID pattern matching (after variables are extracted)
file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - External ID extracted: '$externalId'\n",
FILE_APPEND | LOCK_EX
);

file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - Status: '$status'\n",
FILE_APPEND | LOCK_EX
);

// Check if this is a badge payment
if (strpos($externalId, 'hanapp_badge_') === 0) {
// Handle badge payment directly here
error_log("Xendit webhook: Processing badge payment for external_id: $externalId");

// Extract user ID from external ID (format: hanapp_badge_{user_id}_{timestamp})
if (!preg_match('/^hanapp_badge_(\d+)_\d+$/', $externalId, $matches)) {
error_log("Invalid badge external ID format: $externalId");
ob_clean();
echo json_encode(["status" => "ok", "message" => "Invalid external ID format"]);
exit();
}

$userId = (int)$matches[1];
error_log("Badge payment: Extracted user ID: $userId");

// Only process PAID status
if (strtoupper($status) === 'PAID') {
// Update user's badge status directly (same logic as cash-in updates balance)
$subscriptionStart = date('Y-m-d');
$subscriptionEnd = date('Y-m-d', strtotime('+1 month'));
$nextPaymentDue = date('Y-m-d', strtotime('+1 month'));

$stmt = $conn->prepare("
UPDATE users
SET
badge_status = 'verified',
badge_acquired = 1,
badge_subscription_status = 'active',
badge_subscription_start = ?,
badge_subscription_end = ?,
badge_next_payment_due = ?,
badge_last_payment_date = NOW(),
updated_at = NOW()
WHERE id = ?
");

if ($stmt === false) {
error_log("Failed to prepare badge update statement");
ob_clean();
echo json_encode(["status" => "error", "message" => "Database error"]);
exit();
}

$stmt->bind_param("sssi", $subscriptionStart, $subscriptionEnd, $nextPaymentDue, $userId);

if ($stmt->execute()) {
$affectedRows = $stmt->affected_rows;
$stmt->close();

error_log("Badge payment: Successfully updated user $userId badge status. Affected rows: $affectedRows");

ob_clean();
echo json_encode([
"status" => "ok",
"message" => "Badge payment processed successfully",
"user_id" => $userId,
"badge_status" => "verified"
]);
} else {
error_log("Failed to update badge status for user $userId: " . $stmt->error);
$stmt->close();
ob_clean();
echo json_encode(["status" => "error", "message" => "Failed to update badge status"]);
}
} else {
error_log("Badge payment: Non-PAID status received: $status");
ob_clean();
echo json_encode(["status" => "ok", "message" => "Status noted: $status"]);
}
exit();
}

// Check if this is a job payment (support both old and new formats)
file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - Checking if job payment: external_id='$externalId'\n",
FILE_APPEND | LOCK_EX
);

$isOldJobPayment = strpos($externalId, 'job_payment_') === 0;
$isNewJobPayment = strpos($externalId, 'hanapp_jobpay_') === 0;

if ($isOldJobPayment || $isNewJobPayment) {
file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - JOB PAYMENT DETECTED! Format: " . ($isOldJobPayment ? 'OLD' : 'NEW') . "\n",
FILE_APPEND | LOCK_EX
);

error_log("=== JOB PAYMENT WEBHOOK DETECTED ===");
error_log("External ID: $externalId");
error_log("Invoice ID: $invoiceId");
error_log("Status: $status");
error_log("Format: " . ($isOldJobPayment ? 'OLD (job_payment_invoices)' : 'NEW (job_payment_transactions)'));

if ($isOldJobPayment) {
// Handle old format: job_payment_{application_id}_{timestamp}
file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - Processing OLD format job payment\n",
FILE_APPEND | LOCK_EX
);

if (!preg_match('/^job_payment_(\d+)_\d+$/', $externalId, $matches)) {
error_log("Invalid old job payment external ID format: $externalId");
file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - ERROR: Invalid old external ID format\n",
FILE_APPEND | LOCK_EX
);
ob_clean();
echo json_encode(["status" => "ok", "message" => "Invalid external ID format"]);
exit();
}

$applicationId = (int)$matches[1];
error_log("Old job payment: Extracted application ID: $applicationId");
file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - Application ID extracted: $applicationId\n",
FILE_APPEND | LOCK_EX
);

// Search in job_payment_invoices table
file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - Searching job_payment_invoices table for external_id: $externalId\n",
FILE_APPEND | LOCK_EX
);

// Search by external_id only since xendit_invoice_id might be truncated in database
$stmt = $conn->prepare("
SELECT jpi.*, a.doer_id, a.lister_id, a.listing_id, a.listing_type, a.status as app_status,
COALESCE(pl.title, al.title) as listing_title,
u.full_name as doer_name
FROM job_payment_invoices jpi
JOIN applicationsv2 a ON jpi.application_id = a.id
LEFT JOIN listingsv2 pl ON a.listing_id = pl.id AND a.listing_type = 'PUBLIC'
LEFT JOIN asap_listings al ON a.listing_id = al.id AND a.listing_type = 'ASAP'
JOIN users u ON a.doer_id = u.id
WHERE jpi.external_id = ?
");

if ($stmt === false) {
error_log("Failed to prepare old job payment invoice lookup statement");
file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - ERROR: Failed to prepare old database statement\n",
FILE_APPEND | LOCK_EX
);
ob_clean();
echo json_encode(["status" => "error", "message" => "Database error"]);
exit();
}

$stmt->bind_param("s", $externalId);
$stmt->execute();
$result = $stmt->get_result();

file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - Old format database query executed. Rows found: " . $result->num_rows . "\n",
FILE_APPEND | LOCK_EX
);

if ($result->num_rows === 0) {
$stmt->close();
error_log("=== OLD JOB PAYMENT INVOICE NOT FOUND ===");
error_log("Searched for External ID: $externalId");
error_log("Query executed successfully but returned 0 rows");
ob_clean();
echo json_encode(["status" => "ok", "message" => "Old format invoice not found"]);
exit();
}
} else {
// Handle new format: hanapp_jobpay_{lister_id}_{application_id}_{timestamp}
file_put_contents(__DIR__ . '/webhook_debug.log',
date('Y-m-d H:i:s') . " - Processing NEW format job payment - redirecting to dedicated webhook\n",
FILE_APPEND | LOCK_EX
);

error_log("New format job payment detected - this should be handled by xendit_job_payment_webhook.php");
error_log("External ID: $externalId follows new format hanapp_jobpay_*");

// For new format, we should redirect to the dedicated webhook
// But for now, let's return success to prevent Xendit retries
ob_clean();
echo json_encode([
"status" => "ok",
"message" => "New format job payment - should use dedicated webhook",
"note" => "This webhook handles old format. New format should use xendit_job_payment_webhook.php"
]);
exit();
}

$invoice = $result->fetch_assoc();
$stmt->close();

$invoiceDbId = $invoice['id'];
$doerId = $invoice['doer_id'];
$listerId = $invoice['lister_id'];
$listingId = $invoice['listing_id'];
$listingType = $invoice['listing_type'];
$doerFee = floatval($invoice['doer_fee']);
$totalAmount = floatval($invoice['amount']);
$currentStatus = $invoice['status'];
$listingTitle = $invoice['listing_title'];
$doerName = $invoice['doer_name'];

error_log("Job payment invoice details: ID=$invoiceDbId, Application=$applicationId, Doer=$doerId, Lister=$listerId, DoerFee=$doerFee, Total=$totalAmount, Status=$currentStatus");
error_log("Xendit Webhook - Listing Title: '$listingTitle' (type: " . gettype($listingTitle) . ")");

file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - Xendit Webhook Debug\n" .
	"Application ID: $applicationId\n" .
	"Listing Title Retrieved: '$listingTitle' (type: " . gettype($listingTitle) . ")\n" .
	"Raw Invoice Data: " . json_encode($invoice) . "\n\n",
	FILE_APPEND | LOCK_EX
);

// Process based on webhook status
switch (strtoupper($status)) {
case 'PAID':
if ($currentStatus === 'completed') {
// Already processed, return success
ob_clean();
echo json_encode(["status" => "ok", "message" => "Already processed"]);
exit();
}

// Start transaction for atomic operations
$conn->autocommit(false);

try {
// 1. Update job payment invoice status to completed AND update xendit_invoice_id
error_log("=== ATTEMPTING TO UPDATE JOB PAYMENT INVOICE STATUS ===");
error_log("Invoice DB ID: $invoiceDbId");
error_log("Current Status: $currentStatus");
error_log("Updating xendit_invoice_id from database to webhook value: $invoiceId");

$stmt = $conn->prepare("
UPDATE job_payment_invoices
SET status = 'completed', completed_at = NOW(), xendit_invoice_id = ?
WHERE id = ?
");

if ($stmt === false) {
error_log("Failed to prepare invoice update statement: " . $conn->error);
throw new Exception("Failed to prepare invoice update statement");
}

$stmt->bind_param("si", $invoiceId, $invoiceDbId);

if (!$stmt->execute()) {
error_log("Failed to execute invoice update: " . $stmt->error);
throw new Exception("Failed to update invoice status: " . $stmt->error);
}

$affectedRows = $stmt->affected_rows;
error_log("Invoice update executed. Affected rows: $affectedRows");
$stmt->close();

// Log invoice update result to webhook_debug.log
file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - Invoice update result: Affected rows: $affectedRows\n",
	FILE_APPEND | LOCK_EX
);

// 2. Update application status to in_progress (payment confirmed, project starts)
error_log("=== UPDATING APPLICATION STATUS TO IN_PROGRESS ===");
error_log("Application ID: $applicationId");

file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - Starting application status update to in_progress for Application ID: $applicationId\n",
	FILE_APPEND | LOCK_EX
);

$stmt = $conn->prepare("
UPDATE applicationsv2
SET status = 'in_progress',
	payment_confirmed = 1,
	payment_confirmed_at = CURRENT_TIMESTAMP,
	project_start_date = CURRENT_TIMESTAMP
WHERE id = ?
");

if ($stmt === false) {
error_log("Failed to prepare application update statement: " . $conn->error);
file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - ERROR: Failed to prepare application update statement: " . $conn->error . "\n",
	FILE_APPEND | LOCK_EX
);
throw new Exception("Failed to prepare application update statement");
}

$stmt->bind_param("i", $applicationId);

if (!$stmt->execute()) {
error_log("Failed to execute application update: " . $stmt->error);
file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - ERROR: Failed to execute application update: " . $stmt->error . "\n",
	FILE_APPEND | LOCK_EX
);
throw new Exception("Failed to update application status: " . $stmt->error);
}

$appAffectedRows = $stmt->affected_rows;
error_log("Application update executed. Affected rows: $appAffectedRows");
$stmt->close();

// Log application update result to webhook_debug.log
file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - Application status update result: Affected rows: $appAffectedRows (Status changed to in_progress, payment_confirmed=1)\n",
	FILE_APPEND | LOCK_EX
);

// 3. Create transaction record for doer (earning) - but don't update total_profit yet
error_log("=== CREATING DOER TRANSACTION RECORD ===");
error_log("Doer ID: $doerId, Doer Fee: $doerFee");

file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - Creating doer transaction record: Doer ID: $doerId, Amount: ₱$doerFee\n",
	FILE_APPEND | LOCK_EX
);

$stmt = $conn->prepare("
INSERT INTO transactions (user_id, type, amount, description, transaction_date, method, status)
VALUES (?, 'credit', ?, ?, NOW(), 'job_payment', 'completed')
");

if ($stmt === false) {
error_log("Failed to prepare doer transaction statement: " . $conn->error);
file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - ERROR: Failed to prepare doer transaction statement: " . $conn->error . "\n",
	FILE_APPEND | LOCK_EX
);
throw new Exception("Failed to prepare doer transaction statement");
}

$description = "Job earning from application #$applicationId";
$stmt->bind_param("ids", $doerId, $doerFee, $description);

if (!$stmt->execute()) {
error_log("Failed to execute doer transaction: " . $stmt->error);
file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - ERROR: Failed to execute doer transaction: " . $stmt->error . "\n",
	FILE_APPEND | LOCK_EX
);
throw new Exception("Failed to create doer transaction: " . $stmt->error);
}

$transactionId = $conn->insert_id;
$stmt->close();

// Log transaction creation result to webhook_debug.log
file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - Doer transaction created successfully: Transaction ID: $transactionId, Amount: ₱$doerFee (Note: total_profit will be updated when lister confirms completion)\n",
	FILE_APPEND | LOCK_EX
);

// 4. Create notification for doer (project started after payment)
error_log("=== CREATING DOER NOTIFICATION ===");

file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - Creating doer notification for project start: Doer ID: $doerId\n",
	FILE_APPEND | LOCK_EX
);

$stmt = $conn->prepare("
INSERT INTO doer_notifications (
	user_id, sender_id, type, title, content, associated_id,
	conversation_id_for_chat_nav, conversation_lister_id, conversation_doer_id,
	listing_id, listing_type, lister_id, lister_name, is_read, created_at
) VALUES (?, ?, 'project_started', 'Project Started', ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, CURRENT_TIMESTAMP)
");

if ($stmt !== false) {
$notificationContent = "Payment confirmed! The project has started. You will receive ₱" . number_format($doerFee, 2) . " when the lister confirms completion. Good luck!";

// Get lister's full name for the notification
$getListerNameStmt = $conn->prepare("SELECT full_name FROM users WHERE id = ?");
$listerFullName = 'Lister'; // Default fallback
if ($getListerNameStmt !== false) {
	$getListerNameStmt->bind_param("i", $listerId);
	$getListerNameStmt->execute();
	$listerNameResult = $getListerNameStmt->get_result();
	if ($listerNameResult->num_rows > 0) {
		$listerFullName = $listerNameResult->fetch_assoc()['full_name'];
	}
	$getListerNameStmt->close();
}

// We'll update conversation ID later when we find it
$conversationIdForNotification = null;

$stmt->bind_param("iisiiiisiss",
	$doerId,                    // user_id
	$listerId,                  // sender_id (lister who made the payment)
	$notificationContent,       // content
	$applicationId,             // associated_id
	$conversationIdForNotification, // conversation_id_for_chat_nav (will update later)
	$listerId,                  // conversation_lister_id
	$doerId,                    // conversation_doer_id
	$listingId,                 // listing_id
	$listingType,               // listing_type
	$listerId,                  // lister_id
	$listerFullName             // lister_name
);

if (!$stmt->execute()) {
	error_log("Failed to create doer notification: " . $stmt->error);
	file_put_contents(__DIR__ . '/webhook_debug.log',
		date('Y-m-d H:i:s') . " - ERROR: Failed to create doer notification: " . $stmt->error . "\n",
		FILE_APPEND | LOCK_EX
	);
} else {
	$notificationId = $conn->insert_id;
	error_log("Doer notification created successfully with ID: $notificationId");
	file_put_contents(__DIR__ . '/webhook_debug.log',
		date('Y-m-d H:i:s') . " - Doer notification created successfully for Doer ID: $doerId with ID: $notificationId\n",
		FILE_APPEND | LOCK_EX
	);
}
$stmt->close();
} else {
error_log("Failed to prepare doer notification statement");
file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - ERROR: Failed to prepare doer notification statement\n",
	FILE_APPEND | LOCK_EX
);
}

// 5. Send system message to conversation indicating project has started
error_log("=== SENDING SYSTEM MESSAGE TO CONVERSATION ===");

file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - Sending system message to conversation for project start\n",
	FILE_APPEND | LOCK_EX
);

// Get conversation ID for this application
$getConvStmt = $conn->prepare("
SELECT id FROM conversationsv2
WHERE listing_id = ? AND listing_type = ? AND lister_id = ? AND doer_id = ?
");

if ($getConvStmt !== false) {
$getConvStmt->bind_param("isii", $listingId, $listingType, $listerId, $doerId);
$getConvStmt->execute();
$convResult = $getConvStmt->get_result();

if ($convResult->num_rows > 0) {
	$conversationId = $convResult->fetch_assoc()['id'];
	$getConvStmt->close();

	// Update the notification with the conversation ID for chat navigation
	if (isset($notificationId)) {
		$updateNotificationStmt = $conn->prepare("
			UPDATE doer_notifications
			SET conversation_id_for_chat_nav = ?
			WHERE id = ?
		");
		if ($updateNotificationStmt !== false) {
			$updateNotificationStmt->bind_param("ii", $conversationId, $notificationId);
			$updateNotificationStmt->execute();
			$updateNotificationStmt->close();
			error_log("Xendit Webhook: Updated notification $notificationId with conversation ID $conversationId for chat navigation");
			file_put_contents(__DIR__ . '/webhook_debug.log',
				date('Y-m-d H:i:s') . " - Updated notification $notificationId with conversation ID $conversationId for chat navigation\n",
				FILE_APPEND | LOCK_EX
			);
		}
	}

	// Send system message (we already have lister's full name from notification creation)
	$systemMessageContent = "$listerFullName started the project.";
	$sendMessageStmt = $conn->prepare("
		INSERT INTO messagesv2 (conversation_id, sender_id, receiver_id, content, sent_at, type)
		VALUES (?, ?, ?, ?, NOW(), 'system')
	");

	if ($sendMessageStmt !== false) {
		$sendMessageStmt->bind_param("iiis", $conversationId, $listerId, $doerId, $systemMessageContent);

		if ($sendMessageStmt->execute()) {
			error_log("System message sent successfully to conversation $conversationId");
			file_put_contents(__DIR__ . '/webhook_debug.log',
				date('Y-m-d H:i:s') . " - System message sent: '$systemMessageContent' to conversation $conversationId\n",
				FILE_APPEND | LOCK_EX
			);
		} else {
			error_log("Failed to send system message: " . $sendMessageStmt->error);
			file_put_contents(__DIR__ . '/webhook_debug.log',
				date('Y-m-d H:i:s') . " - ERROR: Failed to send system message: " . $sendMessageStmt->error . "\n",
				FILE_APPEND | LOCK_EX
			);
		}
		$sendMessageStmt->close();
	} else {
		error_log("Failed to prepare system message statement");
	}
} else {
	$getConvStmt->close();
	error_log("Conversation not found for listing_id: $listingId, listing_type: $listingType, lister_id: $listerId, doer_id: $doerId");
	file_put_contents(__DIR__ . '/webhook_debug.log',
		date('Y-m-d H:i:s') . " - WARNING: Conversation not found for application $applicationId\n",
		FILE_APPEND | LOCK_EX
	);
}
} else {
	error_log("Failed to prepare conversation lookup statement");
	file_put_contents(__DIR__ . '/webhook_debug.log',
		date('Y-m-d H:i:s') . " - ERROR: Failed to prepare conversation lookup statement\n",
		FILE_APPEND | LOCK_EX
	);
}

// Commit transaction
$conn->commit();
$conn->autocommit(true);

error_log("=== JOB PAYMENT PROCESSING COMPLETED SUCCESSFULLY ===");
error_log("Application ID: $applicationId");
error_log("Invoice DB ID: $invoiceDbId");
error_log("Invoice status changed from '$currentStatus' to 'completed'");
error_log("Application status changed to 'in_progress'");
error_log("Doer fee reserved for completion: $doerFee");
error_log("Note: total_profit will be updated when lister confirms completion");

// Log final success to webhook_debug.log
file_put_contents(__DIR__ . '/webhook_debug.log',
	date('Y-m-d H:i:s') . " - ✅ JOB PAYMENT PROCESSING COMPLETED SUCCESSFULLY!\n" .
	"  - Application ID: $applicationId\n" .
	"  - Invoice DB ID: $invoiceDbId\n" .
	"  - Invoice status: '$currentStatus' → 'completed'\n" .
	"  - Application status: → 'in_progress'\n" .
	"  - Payment confirmed: YES\n" .
	"  - Project start date: SET\n" .
	"  - Doer fee reserved: ₱$doerFee\n" .
	"  - Transaction record created: YES\n" .
	"  - Doer notification sent: YES\n" .
	"  - Note: total_profit will be updated when lister confirms completion\n",
	FILE_APPEND | LOCK_EX
);

ob_clean();
echo json_encode([
"status" => "ok",
"message" => "Job payment processed successfully - project started",
"application_id" => $applicationId,
"invoice_id" => $invoiceDbId,
"old_invoice_status" => $currentStatus,
"new_invoice_status" => "completed",
"application_status" => "in_progress",
"doer_fee_reserved" => $doerFee,
"note" => "Doer will receive payment when lister confirms completion"
]);

} catch (Exception $e) {
// Rollback on error
$conn->rollback();
$conn->autocommit(true);
error_log("Job payment processing error: " . $e->getMessage());
ob_clean();
echo json_encode(["status" => "error", "message" => "Failed to process payment: " . $e->getMessage()]);
}
break;

case 'FAILED':
// Update invoice status to failed
$stmt = $conn->prepare("
UPDATE job_payment_invoices
SET status = 'failed', failed_at = NOW()
WHERE id = ?
");

if ($stmt === false) {
error_log("Failed to prepare job payment failed update statement");
ob_clean();
echo json_encode(["status" => "error", "message" => "Database error"]);
exit();
}

$stmt->bind_param("i", $invoiceDbId);
$stmt->execute();
$stmt->close();

error_log("Job payment failed: Application $applicationId, Invoice $invoiceDbId");

ob_clean();
echo json_encode(["status" => "ok", "message" => "Job payment marked as failed"]);
break;

case 'EXPIRED':
// Update invoice status to expired
$stmt = $conn->prepare("
UPDATE job_payment_invoices
SET status = 'expired'
WHERE id = ?
");

if ($stmt === false) {
error_log("Failed to prepare job payment expired update statement");
ob_clean();
echo json_encode(["status" => "error", "message" => "Database error"]);
exit();
}

$stmt->bind_param("i", $invoiceDbId);
$stmt->execute();
$stmt->close();

error_log("Job payment expired: Application $applicationId, Invoice $invoiceDbId");

ob_clean();
echo json_encode(["status" => "ok", "message" => "Job payment marked as expired"]);
break;

default:
error_log("Unknown job payment webhook status: $status for application $applicationId");
ob_clean();
echo json_encode(["status" => "ok", "message" => "Status noted: $status"]);
}
exit();
}

// Find the cash-in transaction in our database
$stmt = $conn->prepare("
SELECT id, user_id, amount, status
FROM transactions
WHERE xendit_invoice_id = ? AND xendit_external_id = ? AND type = 'cash_in'
");

if ($stmt === false) {
throw new Exception("Failed to prepare transaction lookup statement");
}

$stmt->bind_param("ss", $invoiceId, $externalId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
$stmt->close();
error_log("Cash-in transaction not found for invoice: $invoiceId, external: $externalId");
// Return success to prevent Xendit from retrying
ob_clean();
echo json_encode(["status" => "ok", "message" => "Transaction not found"]);
exit();
}

$transaction = $result->fetch_assoc();
$stmt->close();

$transactionId = $transaction['id'];
$userId = $transaction['user_id'];
$originalAmount = floatval($transaction['amount']);
$currentStatus = $transaction['status'];

// Process based on webhook status
switch (strtoupper($status)) {
case 'PAID':
if ($currentStatus === 'completed') {
// Already processed, return success
ob_clean();
echo json_encode(["status" => "ok", "message" => "Already processed"]);
exit();
}

// Start transaction for atomic operations
$conn->autocommit(false);

try {
// Update transaction status (simplified for existing table structure)
$stmt = $conn->prepare("
UPDATE transactions
SET status = 'completed',
updated_at = CURRENT_TIMESTAMP
WHERE id = ?
");

if ($stmt === false) {
throw new Exception("Failed to prepare transaction update statement");
}

$stmt->bind_param("i", $transactionId);

if (!$stmt->execute()) {
throw new Exception("Failed to update transaction: " . $stmt->error);
}
$stmt->close();

// Get current balance before update (for verification)
$stmt = $conn->prepare("SELECT balance FROM users WHERE id = ?");
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$oldBalance = floatval($user['balance']);
$stmt->close();

// Calculate base amount (excluding ₱20 transaction fee)
$transactionFee = 20.00;
$baseAmount = $originalAmount - $transactionFee;

// Ensure we don't add negative amounts (safety check)
if ($baseAmount <= 0) {
throw new Exception("Invalid amount calculation: base amount would be negative");
}

error_log("Cash-in webhook: Total amount: $originalAmount, Fee: $transactionFee, Base amount to add: $baseAmount");

// Update user balance with base amount only (excluding fee)
$stmt = $conn->prepare("
UPDATE users
SET balance = balance + ?, updated_at = CURRENT_TIMESTAMP
WHERE id = ?
");

if ($stmt === false) {
throw new Exception("Failed to prepare balance update statement");
}

$stmt->bind_param("di", $baseAmount, $userId);

if (!$stmt->execute()) {
throw new Exception("Failed to update user balance: " . $stmt->error);
}

// Verify the update worked correctly
if ($stmt->affected_rows !== 1) {
throw new Exception("Balance update affected unexpected number of rows: " . $stmt->affected_rows);
}
$stmt->close();

// Double-check the new balance
$stmt = $conn->prepare("SELECT balance FROM users WHERE id = ?");
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$newBalance = floatval($user['balance']);
$stmt->close();

$expectedBalance = $oldBalance + $baseAmount;
if (abs($newBalance - $expectedBalance) > 0.01) {
throw new Exception("Balance verification failed. Expected: $expectedBalance, Got: $newBalance");
}

// Commit transaction
$conn->commit();
$conn->autocommit(true);

error_log("Cash-in completed: User $userId, Total Amount: $originalAmount, Base Amount Added: $baseAmount, Fee: $transactionFee, Transaction $transactionId, Old Balance: $oldBalance, New Balance: $newBalance");

ob_clean();
echo json_encode([
"status" => "ok",
"message" => "Payment processed successfully",
"transaction_id" => $transactionId,
"user_id" => $userId,
"total_amount" => $originalAmount,
"base_amount" => $baseAmount,
"transaction_fee" => $transactionFee,
"old_balance" => $oldBalance,
"new_balance" => $newBalance,
"balance_verified" => true
]);

} catch (Exception $e) {
// Rollback on error
$conn->rollback();
$conn->autocommit(true);
throw $e;
}
break;

case 'EXPIRED':
// Update transaction status to expired
$stmt = $conn->prepare("
UPDATE transactions
SET status = 'expired', updated_at = CURRENT_TIMESTAMP
WHERE id = ?
");

if ($stmt === false) {
throw new Exception("Failed to prepare expired update statement");
}

$stmt->bind_param("i", $transactionId);
$stmt->execute();
$stmt->close();

error_log("Cash-in expired: Transaction $transactionId");

ob_clean();
echo json_encode(["status" => "ok", "message" => "Transaction marked as expired"]);
break;

case 'FAILED':
// Update transaction status to failed
$stmt = $conn->prepare("
UPDATE transactions
SET status = 'failed', updated_at = CURRENT_TIMESTAMP
WHERE id = ?
");

if ($stmt === false) {
throw new Exception("Failed to prepare failed update statement");
}

$stmt->bind_param("i", $transactionId);
$stmt->execute();
$stmt->close();

error_log("Cash-in failed: Transaction $transactionId");

ob_clean();
echo json_encode(["status" => "ok", "message" => "Transaction marked as failed"]);
break;

default:
error_log("Unknown webhook status: $status for transaction $transactionId");
ob_clean();
echo json_encode(["status" => "ok", "message" => "Status noted"]);
}

} catch (Exception $e) {
error_log("Xendit Webhook Error: " . $e->getMessage());
ob_clean();
http_response_code(500);
echo json_encode([
"error" => "Webhook processing failed",
"message" => $e->getMessage()
]);
} finally {
if (isset($conn)) {
$conn->close();
}
}
?>