<?php
// hanapp_backend/api/verification/submit_badge_id_verification.php
// <PERSON>les submission of badge ID verification (ID photos only, no barangay clearance)

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

require_once '../../config/db_connect.php';

try {
    // Get POST data
    $userId = $_POST['user_id'] ?? null;
    $idType = $_POST['id_type'] ?? null;
    $confirmation = $_POST['confirmation'] ?? null;

    // Debug: Log received data
    error_log("submit_badge_id_verification.php: Received data - User ID: $userId, ID Type: $idType, Confirmation: $confirmation");
    error_log("submit_badge_id_verification.php: Files received: " . print_r($_FILES, true));

    // Validate required fields
    if (!$userId || !$idType || $confirmation === null) {
        throw new Exception('Missing required fields: user_id, id_type, or confirmation');
    }

    // Validate files
    if (!isset($_FILES['id_photo_front']) || !isset($_FILES['id_photo_back'])) {
        throw new Exception('Missing required files: id_photo_front and id_photo_back');
    }

    // Validate file uploads
    if ($_FILES['id_photo_front']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('Front ID photo upload failed: ' . $_FILES['id_photo_front']['error']);
    }

    if ($_FILES['id_photo_back']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('Back ID photo upload failed: ' . $_FILES['id_photo_back']['error']);
    }

    // Create upload directories (same as normal verification)
    $uploadDirs = [
        'id_front' => '../../uploads/front/images/',
        'id_back' => '../../uploads/back/images/'
    ];

    foreach ($uploadDirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
    }

    // Function to upload and validate image
    function uploadImage($file, $uploadDir, $prefix) {
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, $allowedTypes)) {
            throw new Exception("Invalid file type for $prefix. Only JPEG and PNG are allowed.");
        }

        // Validate file size (max 10MB)
        if ($file['size'] > 10 * 1024 * 1024) {
            throw new Exception("File size too large for $prefix. Maximum 10MB allowed.");
        }

        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = $prefix . '_' . uniqid() . '.' . $extension;
        $filepath = $uploadDir . $filename;

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception("Failed to upload $prefix");
        }

        // Return relative path for database storage (same format as normal verification)
        $relativePath = str_replace('../../', '', $uploadDir) . $filename;
        return $relativePath;
    }

    // Upload files using same directory structure as normal verification
    $uploadedFiles = [];
    $uploadedFiles['id_photo_front'] = uploadImage($_FILES['id_photo_front'], $uploadDirs['id_front'], 'badge_front_' . $userId);
    $uploadedFiles['id_photo_back'] = uploadImage($_FILES['id_photo_back'], $uploadDirs['id_back'], 'badge_back_' . $userId);

    // Debug: Log uploaded files
    error_log("submit_badge_id_verification.php: Uploaded files: " . print_r($uploadedFiles, true));

    // Start database transaction
    $conn->begin_transaction();

    // Update users table with photo URLs (no brgy clearance for badge verification)
    $updateUserStmt = $conn->prepare("
        UPDATE users 
        SET 
            id_photo_front_url = ?,
            id_photo_back_url = ?,
            verification_status = 'pending',
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ");
    
    if (!$updateUserStmt) {
        throw new Exception("Failed to prepare user update statement: " . $conn->error);
    }

    $updateUserStmt->bind_param("ssi", 
        $uploadedFiles['id_photo_front'], 
        $uploadedFiles['id_photo_back'], 
        $userId
    );

    if (!$updateUserStmt->execute()) {
        throw new Exception("Failed to update user: " . $updateUserStmt->error);
    }

    error_log("submit_badge_id_verification.php: Updated user $userId with badge ID photos");

    // Check if verification record exists
    $checkStmt = $conn->prepare("SELECT id FROM verifications WHERE user_id = ?");
    if (!$checkStmt) {
        throw new Exception("Failed to prepare check statement: " . $conn->error);
    }

    $checkStmt->bind_param("i", $userId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();

    if ($result->num_rows > 0) {
        // Update existing verification record - sync all photos to match users table (no brgy clearance)
        $updateVerificationStmt = $conn->prepare("
            UPDATE verifications
            SET
                id_type = ?,
                id_photo_front_url = ?,
                id_photo_back_url = ?,
                brgy_clearance_photo_url = NULL,
                face_scan_photo_url = (SELECT live_photo_url FROM users WHERE id = ?),
                confirmation_status = ?,
                status = 'pending',
                submitted_at = CURRENT_TIMESTAMP,
                reviewed_at = NULL,
                reviewer_id = NULL,
                rejection_reason = NULL
            WHERE user_id = ?
        ");

        if (!$updateVerificationStmt) {
            throw new Exception("Failed to prepare verification update statement: " . $conn->error);
        }

        $confirmationBool = ($confirmation === 'true' || $confirmation === true) ? 1 : 0;

        $updateVerificationStmt->bind_param("sssiii",
            $idType,
            $uploadedFiles['id_photo_front'],
            $uploadedFiles['id_photo_back'],
            $userId, // for face_scan_photo_url subquery
            $confirmationBool,
            $userId  // for WHERE clause
        );

        if (!$updateVerificationStmt->execute()) {
            throw new Exception("Failed to update verification: " . $updateVerificationStmt->error);
        }

        error_log("submit_badge_id_verification.php: Updated verification record for user $userId - badge verification");
    } else {
        // Create new verification record - sync all photos to match users table (no brgy clearance)
        $insertVerificationStmt = $conn->prepare("
            INSERT INTO verifications (
                user_id,
                id_type,
                id_photo_front_url,
                id_photo_back_url,
                brgy_clearance_photo_url,
                face_scan_photo_url,
                confirmation_status,
                status,
                submitted_at
            ) VALUES (?, ?, ?, ?, NULL, (SELECT live_photo_url FROM users WHERE id = ?), ?, 'pending', CURRENT_TIMESTAMP)
        ");

        if (!$insertVerificationStmt) {
            throw new Exception("Failed to prepare verification insert statement: " . $conn->error);
        }

        $confirmationBool = ($confirmation === 'true' || $confirmation === true) ? 1 : 0;

        $insertVerificationStmt->bind_param("isssii",
            $userId,
            $idType,
            $uploadedFiles['id_photo_front'],
            $uploadedFiles['id_photo_back'],
            $userId, // for face_scan_photo_url subquery
            $confirmationBool
        );

        if (!$insertVerificationStmt->execute()) {
            throw new Exception("Failed to insert verification: " . $insertVerificationStmt->error);
        }

        error_log("submit_badge_id_verification.php: Created new verification record for user $userId - badge verification");
    }

    // Commit transaction
    $conn->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Badge ID verification submitted successfully! Please complete face verification.',
        'data' => [
            'id_photo_front_url' => $uploadedFiles['id_photo_front'],
            'id_photo_back_url' => $uploadedFiles['id_photo_back']
        ]
    ]);

} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($conn)) {
        $conn->rollback();
    }
    
    error_log("submit_badge_id_verification.php: Error - " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    // Close database connection
    if (isset($conn)) {
        $conn->close();
    }
}
?>
