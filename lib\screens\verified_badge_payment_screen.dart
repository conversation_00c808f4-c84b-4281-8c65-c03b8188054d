import 'package:flutter/material.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/services/badge_payment_service.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/models/user.dart';
import 'package:url_launcher/url_launcher.dart';

class VerifiedBadgePaymentScreen extends StatefulWidget {
  const VerifiedBadgePaymentScreen({Key? key}) : super(key: key);

  @override
  State<VerifiedBadgePaymentScreen> createState() => _VerifiedBadgePaymentScreenState();
}

class _VerifiedBadgePaymentScreenState extends State<VerifiedBadgePaymentScreen> {
  final BadgePaymentService _badgePaymentService = BadgePaymentService();

  String? _selectedPaymentMethod;
  String? _selectedBank;
  bool _isLoading = false;
  User? _currentUser;

  // Badge pricing and fees
  static const double _badgePrice = 99.00;
  static const double _gcashFee = 2.30;
  static const double _cardFee = 17.00;
  static const double _bankFee = 17.00;

  // Working banks - exact same as cash_in screen
  final List<Map<String, String>> _workingBanks = [
    {'id': 'bpi', 'name': 'BPI Direct Debit', 'icon': '🏦'},
    {'id': 'chinabank', 'name': 'China Bank Direct Debit', 'icon': '🏦'},
    {'id': 'rcbc', 'name': 'RCBC Direct Debit', 'icon': '🏦'},
    {'id': 'unionbank', 'name': 'UBP Direct Debit', 'icon': '🏦'},
  ];

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    try {
      final user = await AuthService.getUser();
      setState(() {
        _currentUser = user;
      });
    } catch (e) {
      print('Error loading user: $e');
    }
  }

  String _getPaymentMethodName(String method) {
    switch (method) {
      case 'gcash':
        return 'GCash E-Wallet';
      case 'card':
        return 'Debit/Credit Card';
      case 'bpi':
        return 'BPI Bank Transfer';
      case 'chinabank':
        return 'China Bank Transfer';
      case 'rcbc':
        return 'RCBC Bank Transfer';
      case 'unionbank':
        return 'UnionBank Transfer';
      default:
        return method.toUpperCase();
    }
  }

  // NEW: Calculate transaction fee based on payment method
  double _getTransactionFee() {
    switch (_selectedPaymentMethod) {
      case 'gcash':
        return _gcashFee;
      case 'card':
        return _cardFee;
      case 'bank_transfer':
        return _bankFee;
      default:
        return 0.0;
    }
  }

  // NEW: Calculate total amount (badge price + transaction fee)
  double _getTotalAmount() {
    return _badgePrice + _getTransactionFee();
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _processPayment() async {
    if (_selectedPaymentMethod == null) {
      _showSnackBar('Please select a payment method', isError: true);
      return;
    }

    if (_selectedPaymentMethod == 'bank_transfer' && _selectedBank == null) {
      _showSnackBar('Please select a bank', isError: true);
      return;
    }

    if (_currentUser == null) {
      _showSnackBar('User not found. Please try again.', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Determine payment method - same logic as cash_in
      String paymentMethod = _selectedPaymentMethod!;
      if (_selectedPaymentMethod == 'bank_transfer' && _selectedBank != null) {
        paymentMethod = _selectedBank!;
      }

      final response = await _badgePaymentService.createBadgePayment(
        userId: _currentUser!.id,
        paymentMethod: paymentMethod,
      );

      if (response['success'] == true) {
        final invoiceUrl = response['data']['invoice_url'];

        // Launch payment URL
        if (await canLaunchUrl(Uri.parse(invoiceUrl))) {
          await launchUrl(
            Uri.parse(invoiceUrl),
            mode: LaunchMode.externalApplication,
          );

          // Payment link opened successfully

          // Wait a bit then return to previous screen
          await Future.delayed(const Duration(seconds: 2));
          if (mounted) {
            Navigator.of(context).pop();
          }
        } else {
          throw Exception('Could not launch payment URL');
        }
      } else {
        throw Exception(response['message'] ?? 'Failed to create payment');
      }
    } catch (e) {
      _showSnackBar('Error: ${e.toString()}', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }



  // Helper methods - exact same as cash_in screen
  Widget _buildPaymentOption(String title, String value) {
    final isSelected = _selectedPaymentMethod == value;

    return InkWell(
      onTap: () {
        setState(() {
          if (_selectedPaymentMethod == value) {
            _selectedPaymentMethod = null;
            _selectedBank = null;
          } else {
            _selectedPaymentMethod = value;
            if (value != 'bank_transfer') {
              _selectedBank = null;
            }
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Constants.primaryColor : Colors.grey.shade300,
                  width: 2,
                ),
                color: isSelected ? Constants.primaryColor : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(Icons.check, color: Colors.white, size: 16)
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: isSelected ? Constants.primaryColor : Colors.black87,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankTransferOption() {
    final isSelected = _selectedPaymentMethod == 'bank_transfer';

    return Column(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              if (_selectedPaymentMethod == 'bank_transfer') {
                _selectedPaymentMethod = null;
                _selectedBank = null;
              } else {
                _selectedPaymentMethod = 'bank_transfer';
              }
            });
          },
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? Constants.primaryColor : Colors.grey.shade300,
                      width: 2,
                    ),
                    color: isSelected ? Constants.primaryColor : Colors.transparent,
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 16)
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Bank Transfer',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Constants.primaryColor : Colors.black87,
                    ),
                  ),
                ),
                Icon(
                  isSelected ? Icons.expand_less : Icons.expand_more,
                  color: isSelected ? Constants.primaryColor : Colors.grey,
                ),
              ],
            ),
          ),
        ),
        if (isSelected) ...[
          Container(
            margin: const EdgeInsets.only(left: 60, right: 20, bottom: 10),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                isExpanded: true,
                value: _selectedBank,
                hint: const Text('Select your bank'),
                items: _workingBanks.map((bank) {
                  return DropdownMenuItem<String>(
                    value: bank['id'] as String,
                    child: Row(
                      children: [
                        Text(bank['icon'] as String, style: const TextStyle(fontSize: 20)),
                        const SizedBox(width: 12),
                        Expanded(child: Text(bank['name'] as String)),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (String? value) {
                  setState(() {
                    _selectedBank = value;
                  });
                },
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1,
      color: Colors.grey.shade200,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Verified Badge Subscription'),
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.verified,
                    size: 60,
                    color: Constants.primaryColor,
                  ),
                  const SizedBox(height: 15),
                  const Text(
                    'Tapp Verified Badge',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'Get priority in search results and build trust with clients',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 25),

            // Order Summary
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Order Summary',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 15),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Verified Badge (Monthly)',
                        style: TextStyle(fontSize: 16),
                      ),
                      Text(
                        '₱${_badgePrice.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  if (_selectedPaymentMethod != null && _getTransactionFee() > 0) ...[
                    const SizedBox(height: 10),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Transaction Fee (${_getPaymentMethodName(_selectedPaymentMethod!)})',
                          style: const TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                        Text(
                          '₱${_getTransactionFee().toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ],
                  const Divider(height: 30),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Total',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '₱${_getTotalAmount().toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Constants.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'Subscription will auto-renew monthly',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 25),

            // Payment Method Selection - exact same design as cash_in screen
            const Text(
              'Payment Method',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),

            // Payment Methods Container - exact same design as cash_in screen
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  _buildPaymentOption('GCash E-Wallet', 'gcash'),
                  _buildDivider(),
                  _buildPaymentOption('Debit/Credit Card', 'card'),
                  _buildDivider(),
                  _buildBankTransferOption(),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // Pay Button - same style as cash_in screen
            Container(
              width: double.infinity,
              height: 50,
              margin: const EdgeInsets.symmetric(horizontal: 0),
              child: ElevatedButton(
                onPressed: (_isLoading || _selectedPaymentMethod == null) ? null : _processPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: (_selectedPaymentMethod == null)
                      ? Colors.grey.shade300
                      : Constants.primaryColor,
                  foregroundColor: (_selectedPaymentMethod == null)
                      ? Colors.grey.shade600
                      : Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: _isLoading
                    ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
                    : Text(
                  _selectedPaymentMethod == null
                      ? 'Select Payment Method'
                      : 'Subscribe ₱${_getTotalAmount().toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }


}
